//routes/api/schedule/+server.js
import { json } from '@sveltejs/kit';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

export async function GET({ locals, setHeaders }) {
  try {
    // Calculate the current calendar week (Monday to Sunday)
    const now = new Date();

    // Get the start of the current week (Monday)
    const currentDay = now.getDay();
    const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Handle Sunday (0) as last day of week
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() + mondayOffset);
    startOfWeek.setHours(0, 0, 0, 0);

    // Get the end of the current week (Sunday)
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    const { data: scheduleData, error } = await supabase
      .rpc('get_weekly_schedule', {
        start_timestamp: startOfWeek.getTime(),
        end_timestamp: endOfWeek.getTime()
      });
    if (error) throw error;

    // Filter out anime with "NOT_YET_RELEASED" status
    const filteredScheduleData = scheduleData.filter(show => {
      return show.status !== 'NOT_YET_RELEASED'
    });

    // Process the data into the expected format
    const processedData = filteredScheduleData.map(show => ({
      id: show.id,
      mal_id: show.mal_id || null,
      title: show.title,
      englishTitle: show.english_title,
      poster: show.image,
      background: show.banner_image || null,
      synopsis: show.description || null,
      rating: show.average_score ? show.average_score / 10 : null,
      popularity: show.popularity || 0,
      format: show.format || 'TV',
      seasonYear: show.season_year || new Date().getFullYear(),
      genres: show.genres || [],
      totalEpisodes: show.total_episodes || null,
      nextAiringEpisode: {
        episode: parseInt(show.episode),
        airingAt: new Date(show.airing_time).getTime()
      },
      hasAired: show.status === 'aired',
      isDelayed: show.is_delayed,
      airing_schedule_offset: show.airing_schedule_offset
    }));

    setHeaders({
      'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
    });
    return json({ scheduleData: processedData });

  } catch (err) {
    console.error('Error loading schedule data:', err);
    return new Response(
      JSON.stringify({
        error: 'Failed to load schedule data',
        message: err.message,
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}