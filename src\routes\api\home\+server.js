//src/routes/api/home/<USER>
import { json } from '@sveltejs/kit';
import { formatDistanceToNow } from 'date-fns';
import { pl } from 'date-fns/locale';
import { createClient } from '@supabase/supabase-js';
import { PUBLIC_SUPABASE_URL } from '$env/static/public';
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private';

const supabase = createClient(PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

function processHeroData(heroItems) {


  const categories = {
    recentHighQuality: heroItems.filter(show => {
      const releaseDate = new Date(show.latest_episode.airDate);
      const isRecent = Date.now() - releaseDate <= 5 * 24 * 60 * 60 * 1000; // Extended to 5 days
      return isRecent && show.rating >= 7.0; // Lowered from 7.5 to 7.0
    }),
    topRanked: heroItems.filter(show => {
      // Check for seasonal ranking using the raw rankings array
      if (show.rankings?.raw && Array.isArray(show.rankings.raw)) {
        const seasonalRanking = show.rankings.raw.find(r =>
          r.type === 'POPULAR' &&
          r.allTime === false &&
          r.season !== null &&
          r.context === 'most popular');
        return seasonalRanking && seasonalRanking.rank <= 10 && show.rating >= 7.5; // Relaxed: rank ≤ 10, rating ≥ 7.5
      }
      return false;
    }),
    trending: heroItems.filter(show =>
      show.trending > 100 && // Lowered from 500 to 100
      show.rating >= 6.5 // Lowered from 7.0 to 6.5
    ),
    //not currently used
    hidden_gems: heroItems.filter(show =>
      show.rating >= 8.0 && // Changed from impossible 10 to 8.0
      show.popularity < 20000 && // Increased from 800 to 20000
      show.trending < 100 // Lowered from 300 to 100
    ),
    regular: heroItems.filter(show =>
      show.latest_episode.airDate &&
      new Date(show.latest_episode.airDate) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    )
  };



  const finalSelection = [];

  if (categories.recentHighQuality.length) {
    const randomIndex = Math.floor(Math.random() * categories.recentHighQuality.length);
    finalSelection.push({
      ...categories.recentHighQuality[randomIndex],
      heroReason: 'Nowy Wysoko Oceniany'
    });
  }

  if (categories.topRanked.length) {
    // Filter out shows that are already in finalSelection
    const availableShows = categories.topRanked.filter(s =>
      !finalSelection.some(f => f.id === s.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      // Get the seasonal ranking
      const seasonalRanking = show.rankings?.raw?.find(r =>
        r.type === 'POPULAR' &&
        r.allTime === false &&
        r.season !== null &&
        r.context === 'most popular');

      finalSelection.push({
        ...show,
        heroReason: seasonalRanking ? `#${seasonalRanking.rank} W tym sezonie` : 'Wysoka Ocena'
      });
    }
  }

  if (categories.trending.length) {
    // Filter out shows that are already in finalSelection
    const availableShows = categories.trending.filter(s =>
      !finalSelection.some(f => f.id === s.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      finalSelection.push({
        ...show,
        heroReason: 'Popularne'
      });
    }
  }

  if (categories.hidden_gems.length) {
    // Filter out shows that are already in finalSelection
    const availableShows = categories.hidden_gems.filter(s =>
      !finalSelection.some(f => f.id === s.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      finalSelection.push({
        ...show,
        heroReason: 'Ukryty Klejnot'
      });
    }
  }

  // Check if we have any items from special categories
  const hasOtherCategories = categories.recentHighQuality.length > 0 ||
                            categories.topRanked.length > 0 ||
                            categories.trending.length > 0 ||
                            categories.hidden_gems.length > 0;

  // If we have other categories, add only one regular anime
  // If we don't have other categories, we'll fill with regular anime later
  if (categories.regular.length && hasOtherCategories && finalSelection.length < 5) {
    const availableShows = categories.regular.filter(show =>
      !finalSelection.some(f => f.id === show.id)
    );

    if (availableShows.length) {
      const randomIndex = Math.floor(Math.random() * availableShows.length);
      const show = availableShows[randomIndex];

      finalSelection.push({
        ...show,
        heroReason: 'Z ostatnich 7 dni'
      });
    }
  }

  // Create remaining pool to fill up to 5 items
  const remainingPool = heroItems
    .filter(show => !finalSelection.some(f => f.id === show.id) && show.rating >= 1)
    .sort((a, b) => {
      if (hasOtherCategories) {
        // If we have special categories, prioritize non-regular anime
        const aIsRegular = categories.regular.some(r => r.anilist_id === a.anilist_id);
        const bIsRegular = categories.regular.some(r => r.anilist_id === b.anilist_id);

        if (aIsRegular && !bIsRegular) return 1;  // b comes first
        if (!aIsRegular && bIsRegular) return -1; // a comes first
      }
      return b.rating - a.rating; // sort by rating
    });

  // Count current regular anime in selection
  let regularAnimeCount = finalSelection.filter(show =>
    categories.regular.some(r => r.anilist_id === show.anilist_id)
  ).length;

  // Fill remaining slots up to 5
  while (finalSelection.length < 5 && remainingPool.length) {
    const show = remainingPool.shift();
    const isRegularAnime = categories.regular.some(r => r.anilist_id === show.anilist_id);

    // If we have special categories, limit regular anime to 1
    // If we don't have special categories, allow all regular anime
    if (hasOtherCategories && isRegularAnime && regularAnimeCount >= 1) {
      continue;
    }

    const seasonalRanking = show.rankings?.raw?.find(r =>
      r.type === 'POPULAR' &&
      r.allTime === false &&
      r.season !== null &&
      r.context === 'most popular');

    finalSelection.push({
      ...show,
      heroReason: seasonalRanking ? `#${seasonalRanking.rank} W tym sezonie` : 'Polecane'
    });

    if (isRegularAnime) {
      regularAnimeCount++;
    }
  }

  // If we still don't have 5 items and only have regular anime, fill with them
  if (finalSelection.length < 5 && !hasOtherCategories) {
    const regularPool = categories.regular
      .filter(show => !finalSelection.some(f => f.id === show.id))
      .sort((a, b) => b.rating - a.rating);

    while (finalSelection.length < 5 && regularPool.length) {
      const show = regularPool.shift();
      finalSelection.push({
        ...show,
        heroReason: 'Z ostatnich 7 dni'
      });
    }
  }



  return finalSelection.sort(() => Math.random() - 0.5);
}

export async function GET({ locals }) {
  const currentDate = new Date();
  const currentYear = 2025;
  const currentSeason = 'SPRING';
  const isLoggedIn = !!locals.session;

  try {
    // Define the promises to be resolved
    const promises = [
      supabase.rpc('get_hero_data', {
        current_season: currentSeason,
        current_year: currentYear
      }),
      supabase.rpc('get_new_releases', {
        limit_count: 40
      }),
      supabase.rpc('get_popular_now', {
        current_season: currentSeason,
        current_year: currentYear
      }),
      supabase.rpc('get_upcoming_episodes', {
        timestamp_ms: Date.now()
      }),
      supabase.rpc('get_release_schedule', {
        start_timestamp: currentDate.setHours(0, 0, 0, 0),
        end_timestamp: currentDate.setHours(23, 59, 59, 999)
      })
    ];

    // Only add the fake watching data for non-logged in users
    if (!isLoggedIn) {
      promises.push(
        supabase.rpc('get_continue_watching_fake', {
          limit_count: 12
        })
      );
    }

    const responses = await Promise.all(promises);

    const [
      heroResponse,
      newReleasesResponse,
      popularNowResponse,
      upcomingEpisodesResponse,
      releaseScheduleResponse,
      continueWatchingResponse
    ] = responses;

    // Only check the first 5 responses for errors
    [heroResponse, newReleasesResponse, popularNowResponse,
      upcomingEpisodesResponse, releaseScheduleResponse].forEach(response => {
        if (response.error) throw response.error;
      });

    const heroItems = heroResponse.data.map(anime => ({
      ...anime,
      synopsis: anime.description, // Rename description to synopsis
      short_synopsis: anime.description?.slice(0, 235) +
        (anime.description?.length >= 235 ? '...' : '')
    }));

    const newReleasesData = newReleasesResponse.data.map(release => ({
      id: release.id,
      title: release.title,
      english_title: release.english_title,
      episode: release.episode_number,
      total_episodes: release.total_episodes,
      image: release.thumbnail,
      duration: release.air_date,
      year: release.season_year,
      type: release.format,
      preview: release.preview
    }));

    // Filter out anime with "NOT_YET_RELEASED" status from release schedule
    const filteredReleaseScheduleData = releaseScheduleResponse.data.filter(show =>
      show.status !== 'NOT_YET_RELEASED'
    );

    return json({
      heroData: processHeroData(heroItems),
      newReleasesData,
      popularNowData: popularNowResponse.data,
      upcomingEpisodesData: upcomingEpisodesResponse.data,
      releaseScheduleData: filteredReleaseScheduleData,
      continueWatchingData: !isLoggedIn && continueWatchingResponse ? continueWatchingResponse.data : [],
      isLoggedIn
    });

  } catch (error) {
    console.error('Error in home API:', error);
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error.message,
        heroData: [],
        newReleasesData: [],
        popularNowData: [],
        upcomingEpisodesData: [],
        releaseScheduleData: [],
        continueWatchingData: [],
        isLoggedIn: false
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}